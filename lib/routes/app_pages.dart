import 'package:darve/ui/auth/password/forgot_password_binding.dart';
import 'package:darve/ui/auth/sigin/sign_in_bindings.dart';
import 'package:darve/ui/auth/signup/register_binding.dart';
import 'package:darve/ui/profile/profile_binding.dart';
import 'package:darve/ui/profile/insights/profile_insights_binding.dart';
import 'package:darve/ui/home/<USER>';
import 'package:darve/ui/search/search_page_binding.dart';
import 'package:get/get.dart';
import 'package:darve/ui/auth/sigin/sign_in_page.dart';
import 'package:darve/ui/auth/signup/register_page.dart';
import 'package:darve/ui/auth/password/forgot_password_page.dart';
import 'package:darve/ui/home/<USER>';
import 'package:darve/ui/settings/main/settings_page.dart';
import 'package:darve/ui/settings/privacy/main/privacy_settings.dart';
import 'package:darve/ui/settings/privacy/main/privacy_settings_binding.dart';
import 'package:darve/ui/settings/privacy/password/change_password_page.dart';
import 'package:darve/ui/settings/contact/contact_us_support.dart';
import 'package:darve/ui/settings/info/more_info.dart';
import 'package:darve/ui/search/search_page.dart';
import 'package:darve/ui/notifications/notifications_page.dart';
import 'package:darve/ui/profile/profile_page.dart';
import 'package:darve/ui/profile/insights/profile_insights_page.dart';
import 'package:darve/ui/challenges/my_challenges.dart';
import 'package:darve/ui/camera/camera_page.dart';
import 'package:darve/ui/chat/chat_screen.dart';
import 'package:darve/routes/app_routes.dart';
import 'package:darve/routes/auth_middleware.dart';

class AppPages {
  static final List<GetPage> pages = [
    // Auth routes
    // No middleware needed
    GetPage(
      name: AppRoutes.login,
      page: () => const SignInPage(),
      binding: SignInBindings(),
    ),
    GetPage(
      name: AppRoutes.signup,
      page: () => const RegisterPage(),
      binding: RegisterBinding(),
    ),

    GetPage(
      name: AppRoutes.forgotPassword,
      page: () => const ForgotPasswordPage(),
      binding: ForgotPasswordBinding(),
    ),

    // Home page - requires authentication
    GetPage(
      name: AppRoutes.home,
      page: () => const HomePage(),
      middlewares: [AuthMiddleware()],
      binding: HomeBinding(),
    ),

    // Settings routes - requires authentication
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.privacySettings,
      page: () => const PrivacySettings(),
      binding: PrivacySettingsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.changePassword,
      page: () => const ChangePasswordPage(),
      binding: PrivacySettingsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.contactSupport,
      page: () => const ContactSupportPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.moreInfo,
      page: () => const MoreInfo(),
      middlewares: [AuthMiddleware()],
    ),

    // Feature routes - requires authentication
    GetPage(
      name: AppRoutes.search,
      page: () => const SearchPage(),
      binding: SearchPageBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationsPage(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.profile,
      page: () {
        final args = Get.arguments as Map<String, String>;
        return ProfilePage(
          args['userId']!,
          args['username']!,
          args['imageUrl']!,
        );
      },
      binding: ProfileBinding(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.profileInsights,
      page: () {
        final args = Get.arguments as Map<String, dynamic>;
        return ProfileInsightsPage(
          userId: args['userId'] as String,
          initialTab: args['initialTab'] as int? ?? 0,
        );
      },
      binding: ProfileInsightsBinding(),
      middlewares: [AuthMiddleware()],
    ),

    // Chat screen - requires authentication
    GetPage(
      name: AppRoutes.chat,
      page: () {
        final args = Get.arguments as Map<String, String?>;
        return ChatScreen(
          chatId: args['chatId']!,
          title: args['title']!,
          avatarUrl: args['avatarUrl']!,
          userId: args['userId']!,
        );
      },
      middlewares: [AuthMiddleware()],
    ),

    // Recording routes - requires authentication
    GetPage(
      name: AppRoutes.myChallenges,
      page: () => const MyChallenges(),
      middlewares: [AuthMiddleware()],
    ),

    GetPage(
      name: AppRoutes.camera,
      page: () {
        final args = Get.arguments as Map<String, dynamic>;
        return CameraPage(
          cameras: args['cameras'],
          taskId: args['taskId'],
          cb: args['callback'],
        );
      },
      middlewares: [AuthMiddleware()],
    ),
  ];
}
