import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/result.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class VerifyEmailController extends GetxController {
  final TextEditingController emailController = TextEditingController();
  final TextEditingController confirmEmailController = TextEditingController();
  final RxBool isEmailValid = false.obs;
  final RxBool areEmailsMatching = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString verificationCode = ''.obs;

  // State management using ViewModel pattern for consistency
  final Rx<ViewModel<void>> emailVerificationStartState = const ViewModel<void>.content(null).obs;
  final Rx<ViewModel<void>> emailVerificationConfirmState = const ViewModel<void>.content(null).obs;

  // Legacy reactive properties for backward compatibility
  final RxBool isLoading = false.obs;
  final RxBool isEmailChangedSuccess = false.obs;
  final RxBool isVerificationCodeSent = false.obs;
  final RxBool isVerificationCodeValid = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();
  late final UserRepository _userRepository;

  @override
  void onInit() {
    super.onInit();
    _userRepository = ServiceProvider.userRepository;
  }

  @override
  void onClose() {
    emailController.dispose();
    confirmEmailController.dispose();
    super.onClose();
  }

  bool validateEmail(String email) {
    // Simple regex for email validation
    final RegExp emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  bool matchEmails() {
    return emailController.text.trim() == confirmEmailController.text.trim();
  }

  /// Convenience getters for accessing state data
  bool get isEmailVerificationStartLoading => emailVerificationStartState.value.state == ViewState.loading;
  bool get isEmailVerificationConfirmLoading => emailVerificationConfirmState.value.state == ViewState.loading;
  bool get hasEmailVerificationStartError => emailVerificationStartState.value.state == ViewState.error;
  bool get hasEmailVerificationConfirmError => emailVerificationConfirmState.value.state == ViewState.error;

  Future<void> verificationStart() async {
    if (!validateEmail(emailController.text.trim())) {
      errorMessage.value = 'Invalid email format';
      return;
    }
    if (!matchEmails()) {
      errorMessage.value = 'Emails do not match';
      return;
    }

    // Set loading state
    isLoading.value = true;
    emailVerificationStartState.value = const ViewModel<void>.loading();

    final result = await _userRepository.emailVerificationStart(emailController.text.trim());

    // Use ResultExtensions toViewModel pattern for consistency
    emailVerificationStartState.value = result.toViewModel(
      onError: (error) {
        errorMessage.value = error.message;
        _errorHandler.displayErrorToast(error, 'verificationStart');
      },
      onSuccess: (_) {
        // Success - update legacy reactive properties for backward compatibility
        isVerificationCodeSent.value = true;
        errorMessage.value = '';
      },
    );

    isLoading.value = false;
  }

  Future<void> verificationConfirm() async {
    if (verificationCode.value.isEmpty) {
      errorMessage.value = 'Verification code cannot be empty';
      return;
    }

    // Set loading state
    isLoading.value = true;
    emailVerificationConfirmState.value = const ViewModel<void>.loading();

    final result = await _userRepository.emailVerificationConfirm(
        emailController.text.trim(), verificationCode.value);

    // Use ResultExtensions toViewModel pattern for consistency
    emailVerificationConfirmState.value = result.toViewModel(
      onError: (error) {
        errorMessage.value = error.message;
        _errorHandler.displayErrorToast(error, 'verificationConfirm');
      },
      onSuccess: (_) {
        // Success - update legacy reactive properties for backward compatibility
        isVerificationCodeValid.value = true;
        isEmailChangedSuccess.value = true;
        errorMessage.value = '';
      },
    );

    isLoading.value = false;
  }


}
