import 'package:flutter/material.dart';
import '../components/settings/settings_menu_item.dart';

class ContactSupportPage extends StatelessWidget {
  const ContactSupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Heading
          const Padding(
            padding: EdgeInsets.fromLTRB(24, 8, 20, 8),
            child: Text(
              'Contact Us / Support',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),

          // Menu Items
          SettingsMenuItem(
            text: 'About',
            onTap: () {
              // Handle about
            },
          ),

          SettingsMenuItem(
            text: 'Frequently Asked Questions',
            onTap: () {
              // Handle FAQs
            },
          ),

          SettingsMenuItem(
            text: '24/7 Support',
            onTap: () {
              // Handle 24/7 support
            },
          ),

          SettingsMenuItem(
            text: 'Contact',
            onTap: () {
              // Handle contact
            },
          ),
        ],
      ),
    );
  }
}
