import 'package:darve/ui/components/common/search_field.dart';
import 'package:darve/ui/settings/privacy_settings_controller.dart';
import 'package:darve/ui/settings/two_factor_auth_page.dart';
import 'package:darve/ui/settings/saved_login_page.dart';
import 'package:darve/ui/settings/logged_devices_page.dart';
import 'package:darve/ui/settings/login_alert_page.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/settings/settings_menu_item.dart';

class PrivacySettings extends GetView<PrivacySettingsController> {
  const PrivacySettings({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Search Settings
          SearchField(
            onChanged: (value) {},
          ),

          // Heading
          const Padding(
            padding: EdgeInsets.fromLTRB(24, 8, 20, 8),
            child: Text(
              'Privacy and Security',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),

          // Menu Items
          SettingsMenuItem(
            text: 'Change Password',
            onTap: () {
              RouteHelper.goToChangePassword();
            },
          ),

          SettingsMenuItem(
            text: 'Two Factor Authentication',
            onTap: () {
              Get.to(() => const TwoFactorAuthPage());
            },
          ),

          SettingsMenuItem(
            text: 'Saved Login',
            onTap: () {
              Get.to(() => const SavedLoginPage());
            },
          ),

          SettingsMenuItem(
            text: 'Logged Devices',
            onTap: () {
              Get.to(() => const LoggedDevicesPage());
            },
          ),

          SettingsMenuItem(
            text: 'Login Alert',
            onTap: () {
              Get.to(() => const LoginAlertPage());
            },
          ),
        ],
      ),
    );
  }
}
