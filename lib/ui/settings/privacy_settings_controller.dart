import 'package:darve/api/repositories/user_repository.dart';
import 'package:darve/services/providers/service_provider.dart';
import 'package:darve/ui/core/entity_state_widget.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PrivacySettingsController extends GetxController {
  // Form controllers
  final TextEditingController currentPasswordController = TextEditingController();
  final TextEditingController newPasswordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // State management using ViewModel pattern
  final Rx<ViewModel<bool>> changePasswordState = const ViewModel<bool>.content(false).obs;

  // UI state
  final RxBool isCurrentPasswordVisible = false.obs;
  final RxBool isNewPasswordVisible = false.obs;
  final RxBool isConfirmPasswordVisible = false.obs;

  // Error handling
  final ErrorsHandle _errorHandler = ErrorsHandle();
  late final UserRepository _userRepository;

  @override
  void onInit() {
    super.onInit();
    _userRepository = ServiceProvider.userRepository;
  }

  @override
  void onClose() {
    currentPasswordController.dispose();
    newPasswordController.dispose();
    confirmPasswordController.dispose();
    super.onClose();
  }

  /// Toggle password visibility for current password field
  void toggleCurrentPasswordVisibility() {
    isCurrentPasswordVisible.value = !isCurrentPasswordVisible.value;
  }

  /// Toggle password visibility for new password field
  void toggleNewPasswordVisibility() {
    isNewPasswordVisible.value = !isNewPasswordVisible.value;
  }

  /// Toggle password visibility for confirm password field
  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  /// Validates the current password field
  String? validateCurrentPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Current password is required';
    }
    return null;
  }

  /// Validates the new password field
  String? validateNewPassword(String? value) {
    return Validators.validatePassword(value);
  }

  /// Validates the confirm password field
  String? validateConfirmPassword(String? value) {
    return Validators.validatePasswordConfirmation(value, newPasswordController.text);
  }

  /// Change password functionality
  Future<void> changePassword() async {
    // Validate form first
    if (!formKey.currentState!.validate()) {
      return;
    }

    try {
      // Set loading state
      changePasswordState.value = const ViewModel<bool>.loading();

      // Call API to change password
      await _userRepository.changePassword(
        currentPasswordController.text.trim(),
        newPasswordController.text.trim(),
      );

      // Success state
      changePasswordState.value = const ViewModel<bool>.content(true);

      // Clear form fields
      _clearForm();

      // Show success message
      Get.snackbar(
        'Success',
        'Password changed successfully',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        snackPosition: SnackPosition.TOP,
      );

      // Navigate back after a short delay
      Future.delayed(const Duration(seconds: 1), () {
        Get.back();
      });

    } catch (e) {
      // Set error state with user-friendly message
      final errorMessage = _parsePasswordChangeError(e);
      changePasswordState.value = ViewModel<bool>.error(errorMessage);

      // Also show error toast for debugging
      _errorHandler.displayErrorToast(e, 'changePassword');
    }
  }

  /// Clear all form fields
  void _clearForm() {
    currentPasswordController.clear();
    newPasswordController.clear();
    confirmPasswordController.clear();
  }

  /// Parse password change errors into user-friendly messages
  String _parsePasswordChangeError(dynamic error) {
    final errorString = error.toString();

    if (errorString.contains('401') || errorString.contains('unauthorized')) {
      return 'Current password is incorrect';
    } else if (errorString.contains('400') || errorString.contains('bad request')) {
      return 'Invalid password format';
    } else if (errorString.contains('422') || errorString.contains('validation')) {
      return 'Password does not meet requirements';
    } else if (errorString.contains('timeout') || errorString.contains('network')) {
      return 'Network error. Please check your connection';
    } else if (errorString.contains('500') || errorString.contains('server')) {
      return 'Server error. Please try again later';
    } else {
      return 'Failed to change password. Please try again';
    }
  }

  /// Reset the change password state (for retry functionality)
  void resetChangePasswordState() {
    changePasswordState.value = const ViewModel<bool>.content(false);
  }

  /// Check if form is valid for enabling/disabling submit button
  bool get isFormValid {
    return currentPasswordController.text.isNotEmpty &&
           newPasswordController.text.isNotEmpty &&
           confirmPasswordController.text.isNotEmpty &&
           newPasswordController.text == confirmPasswordController.text;
  }

  /// Get loading state for UI
  bool get isLoading => changePasswordState.value.state == ViewState.loading;
}
