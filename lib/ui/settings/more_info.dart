import 'package:flutter/material.dart';
import '../components/settings/settings_menu_item.dart';

class MoreInfo extends StatelessWidget {
  const MoreInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Settings',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 20,
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Heading
          const Padding(
            padding: EdgeInsets.fromLTRB(24, 8, 20, 8),
            child: Text(
              'More Info',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w400,
                color: Colors.black,
              ),
            ),
          ),

          // Menu Items
          SettingsMenuItem(
            text: 'Privacy Policy',
            onTap: () {
              // Handle Privacy
            },
          ),

          SettingsMenuItem(
            text: 'Terms of Service',
            onTap: () {
              // Handle Terms
            },
          ),

          SettingsMenuItem(
            text: 'Conditions',
            onTap: () {
              // Handle Conditions
            },
          ),

          SettingsMenuItem(
            text: 'Safety Centre',
            onTap: () {
              // Handle Safety Centre
            },
          ),
        ],
      ),
    );
  }
}
